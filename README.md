# BCTV Management System

A comprehensive web application for Beet Curly Top Virus (BCTV) hotspot prediction and eradication support in California agriculture.

## Overview

This Angular-based application provides field workers, researchers, and administrators with tools to:
- Record field observations of host plants and BLH populations
- Document BCTV symptoms and eradication efforts
- Generate AI-powered risk predictions
- Visualize data on interactive maps
- Track and analyze outbreak patterns

## Technology Stack

- **Frontend**: Angular 19 with SSR
- **Backend**: Supabase (PostgreSQL with PostGIS)
- **Mapping**: MapLibre GL JS
- **Storage**: Supabase Storage for photos
- **Authentication**: Supabase Auth

## Features

### ✅ Implemented
- User authentication and role-based access
- Interactive map dashboard with California focus
- Host plant observation forms (10 key BCTV host weeds)
- BLH (Beet Leafhopper) population tracking
- Photo upload with geolocation
- Basic rule-based risk prediction engine
- Mobile-optimized responsive design

### 🚧 In Development
- BCTV symptoms documentation
- Eradication effort tracking
- Advanced map visualizations
- Enhanced prediction algorithms

## Quick Start

### Prerequisites
- Node.js 18+
- Angular CLI 19+
- Supabase account

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd my-angular-app

# Install dependencies
npm install

# Set up environment variables (see Environment Setup below)
# Start development server
ng serve
```

### Environment Setup
Create a Supabase project and update the environment files:

```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  supabase: {
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY'
  },
  maplibre: {
    style: 'https://demotiles.maplibre.org/style.json'
  }
};
```

## ✅ Recent Updates

### Loading Screen Issue - RESOLVED ✅
**Status**: ✅ RESOLVED - Application now loads correctly
**Resolution Date**: January 2025

**What was fixed**:
- ✅ Replaced default Angular template with proper BCTV application shell
- ✅ Added loading spinner and proper routing
- ✅ Fixed authentication flow and service providers
- ✅ Application now loads correctly and redirects to login page

### Phantom Data & Map Display Issues - RESOLVED ✅
**Status**: ✅ RESOLVED - Application now shows real data and updates properly
**Resolution Date**: January 2025

**Issues Fixed**:
- ✅ Removed hardcoded mock data (phantom "Host plant logged 2 hours ago" entries)
- ✅ Removed static risk area statistics (3 very high, 7 high risk areas, etc.)
- ✅ Fixed map display to show newly added observations immediately
- ✅ Implemented auto-refresh when returning from data entry forms
- ✅ Added proper empty state handling for fresh installations

### Map Marker Display Issue - RESOLVED ✅
**Status**: ✅ RESOLVED - Map markers now display correctly for all observations
**Resolution Date**: January 2025

**Issue Fixed**:
- ✅ Map markers now appear at GPS locations where observations were recorded
- ✅ Colored markers display based on observation type (green=host plants, yellow=BLH, red=BCTV, blue=eradication)
- ✅ Interactive popups show observation details when markers are clicked
- ✅ Map controls and styling display properly

**Root Cause**: Missing MapLibre GL CSS import in global styles
**Solution**: Added `@import 'maplibre-gl/dist/maplibre-gl.css';` to `src/styles.scss`

**Technical Details**:
- Replaced static data arrays with dynamic database queries
- Added navigation-based and window focus refresh mechanisms
- Implemented proper error handling and loading states
- Created relative time calculation for activity timestamps
- Added empty state messages for new installations
- Enhanced coordinate validation and error handling for map markers
- Added comprehensive debugging and logging for map functionality

**Related Documentation**:
- `MAP_MARKER_FIX_DOCUMENTATION.md` - Complete map marker resolution details
- `PHANTOM_DATA_FIX_DOCUMENTATION.md` - Data display resolution details
- `LOADING_ISSUE_DEBUG.md` - Previous loading issue resolution
- `DEVELOPMENT_PROGRESS.md` - Updated project status

## Application Structure

```
src/app/
├── core/
│   ├── models/          # Data models and types
│   ├── services/        # Core services (auth, data, etc.)
│   └── guards/          # Route guards
├── features/
│   ├── auth/           # Authentication components
│   ├── dashboard/      # Main dashboard with map
│   ├── data-entry/     # Data collection forms
│   └── predictions/    # Risk prediction views
├── shared/
│   └── components/     # Reusable components
└── environments/       # Environment configurations
```

## Key Components

### Data Models
- **Host Plants**: 10 key BCTV host weeds with density tracking
- **BLH Observations**: Population counts and behavior patterns
- **BCTV Symptoms**: Disease severity and symptom types
- **Eradication Efforts**: Control methods and effectiveness

### Prediction Engine
The application includes a rule-based prediction system that analyzes:
- Host plant density and distribution
- Beet leafhopper populations
- Weather conditions
- Seasonal factors
- Historical outbreak data

### Mobile Features
- GPS integration for accurate location data
- Photo capture and upload
- Offline-capable forms (planned)
- Touch-optimized interface

## User Roles

- **Field Worker**: Data collection and basic reporting
- **Researcher**: Advanced analytics and data export
- **Administrator**: User management and system configuration

## Development

### Running Tests
```bash
# Unit tests
ng test

# E2E tests
ng e2e
```

### Building for Production
```bash
ng build --configuration production
```

### Database Setup
See `DEVELOPMENT_PROGRESS.md` for detailed database schema and setup instructions.

## Contributing

1. Review the `DEVELOPMENT_PROGRESS.md` file for current status
2. Check the roadmap for planned features
3. Follow the established code structure and patterns
4. Ensure mobile responsiveness for all new features
5. Add appropriate tests for new functionality

## License

This project is developed for California agricultural research and management purposes.

## Support

For technical issues or feature requests, please refer to the development roadmap in `DEVELOPMENT_PROGRESS.md`.

---

**Note**: This application is specifically designed for California agricultural use and includes validation for California geographic bounds. The system focuses on the 10 key BCTV host weeds identified as critical for monitoring in California agriculture.
